<template>
  <div class="new-review-package">
    <!-- 表单区域 -->
    <el-form :inline="true" class="form-inline">
      <el-form-item label="销售单号">
        <el-input placeholder="请扫描或输入XSD开头单号,点击回车"></el-input>
      </el-form-item>
      <el-form-item label="随货同行单数">
        <span>2</span>
        <span style="color: red;">未扫：1</span>
      </el-form-item>
      <el-form-item label="耗材码">
        <el-input placeholder="请扫描或hao"></el-input>
      </el-form-item>
      <el-button type="primary">查看任务</el-button>
      <el-button type="primary">刷新</el-button>
      <el-form-item label="快递类型">
        <span style="color: red;">顺丰(易破损)</span>
      </el-form-item>
      <el-form-item label="订单类型">
        <span style="color: blue;">京东</span>
      </el-form-item>
    </el-form>

    <!-- 商品图片区域 -->
    <div class="product-image">
      <img src="https://placehold.co/300x300" alt="商品图片">
      <p>商品图片</p>
    </div>

    <!-- 商品列表表格 -->
    <el-table :data="goodsList" border style="width: 100%">
      <el-table-column prop="code" label="商品条码"></el-table-column>
      <el-table-column prop="name" label="商品名称"></el-table-column>
      <el-table-column prop="batch" label="商品批号"></el-table-column>
      <el-table-column prop="encoding" label="商品编码 (双击复核)"></el-table-column>
      <el-table-column prop="packed" label="已装数"></el-table-column>
      <el-table-column prop="unpacked" label="未装数"></el-table-column>
      <el-table-column prop="total" label="装箱总数"></el-table-column>
    </el-table>

    <!-- 操作按钮 -->
    <div class="operation-buttons">
      <el-button type="primary">复核确认</el-button>
      <el-button type="danger">异常提交</el-button>
      <el-button type="primary">更换承运商</el-button>
    </div>

    <!-- 耗材信息表格 -->
    <el-table :data="materialList" border style="width: 100%">
      <el-table-column prop="code" label="耗材编码"></el-table-column>
      <el-table-column prop="name" label="耗材名称"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button @click="handleDelete(scope.$index)" size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 底部统计信息 -->
    <div class="bottom-stats">
      <p>已装箱商品总数 10 未装箱商品总数 250 商品总数 260</p>
    </div>

    <!-- 商品详细信息 -->
    <div class="product-details">
      <el-form :inline="true">
        <el-form-item label="商品条码">
          <el-input value="69010193485576"></el-input>
        </el-form-item>
        <el-form-item label="数量">
          <el-input></el-input>
        </el-form-item>
        <el-form-item label="商品名称">
          <span>三九感冒灵颗粒 10袋/盒</span>
        </el-form-item>
        <el-form-item label="商品编码">
          <span>********</span>
        </el-form-item>
        <el-form-item label="包装单位">
          <span>盒</span>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      goodsList: [
        {
          code: '69010193485576',
          name: '三九感冒灵颗粒 10袋/盒',
          batch: '250501(20)',
          encoding: '********',
          packed: 0,
          unpacked: 20,
          total: 20
        }
      ],
      materialList: [
        {
          code: 'H001',
          name: '二手纸箱'
        }
      ]
    };
  },
  methods: {
    handleDelete(index) {
      this.materialList.splice(index, 1);
    }
  }
};
</script>

<style scoped>
.new-review-package {
  padding: 20px;
}

.form-inline {
  margin-bottom: 20px;
}

.product-image {
  width: 300px;
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid #ccc;
  margin-bottom: 20px;
}

.product-image img {
  max-width: 100%;
  max-height: 100%;
}

.operation-buttons {
  margin-top: 20px;
  text-align: center;
}

.bottom-stats {
  margin-top: 20px;
  text-align: center;
}

.product-details {
  margin-top: 20px;
}
</style>